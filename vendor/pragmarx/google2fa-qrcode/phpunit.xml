<?xml version="1.0" encoding="UTF-8"?>

<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="tests/bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnError="false"
         stopOnFailure="false"
         verbose="true"
>
    <testsuites>
        <testsuite name="Laravel Test Suite">
            <directory suffix="Test.php">./tests</directory>
        </testsuite>
    </testsuites>

    <logging>
        <log type="coverage-html" target="coverage/" lowUpperBound="35" highLowerBound="70"/>
        <log type="coverage-clover" target="coverage/coverage-clover.xml" lowUpperBound="35" highLowerBound="70"/>
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="true"/>
    </logging>

    <filter>
        <whitelist addUncoveredFilesFromWhitelist="true" processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./src</directory>
            <exclude></exclude>
        </whitelist>
    </filter>
</phpunit>
