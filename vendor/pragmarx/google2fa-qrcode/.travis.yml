dist: trusty
language: php

php:
  - 7.1
  - 7.2
  - 7.3
  - 7.4
  - nightly

# This triggers builds to run on the new TravisCI infrastructure.
# See: http://docs.travis-ci.com/user/workers/container-based-infrastructure/
sudo: false

## Cache composer
cache:
  directories:
    - $HOME/.composer/cache

install:
  - |
    if [[ "$TRAVIS_PHP_VERSION" == 'nightly' ]]; then
      travis_retry composer update --ignore-platform-reqs --no-interaction --prefer-dist
    else
      yes '' | pecl install imagick
      travis_retry composer update --no-interaction --prefer-dist
    fi

script:
  - vendor/bin/phpunit --coverage-text --coverage-clover=coverage.clover

after_script:
  - |
    if [[ "$TRAVIS_PHP_VERSION" == '7.4' ]]; then
      wget https://scrutinizer-ci.com/ocular.phar
      php ocular.phar code-coverage:upload --format=php-clover coverage.clover
    fi
