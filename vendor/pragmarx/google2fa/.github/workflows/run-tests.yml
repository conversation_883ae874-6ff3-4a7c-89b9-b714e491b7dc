name: Run tests

on:
  push:
  pull_request:
  schedule:
    - cron: '0 0 * * *'

jobs:
  php-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php: [8.4, 8.3, 8.2, 8.1, 8.0, 7.4]

    name: P${{ matrix.php }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install dependencies
        run: |
          git config --global user.email "<EMAIL>"; git config --global user.name "<PERSON>"

      - name: Install dependencies
        run: |
          composer require --prefer-dist --no-interaction --no-suggest

      - name: Execute tests
        run: vendor/bin/phpunit

      - name: Execute PHPStan
        run: vendor/bin/phpstan analyse -c phpstan.neon
