<?php

namespace Drupal\alogin\Controller;

use Drupal\Core\Access\CsrfTokenGenerator;
use <PERSON><PERSON><PERSON>\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON>al\Core\DependencyInjection\ContainerInjectionInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Routing\RouteProviderInterface;
use Dr<PERSON>al\alogin\AuthenticatorService;
use <PERSON><PERSON><PERSON>\user\Controller\UserAuthenticationController;
use Drupal\user\UserAuthenticationInterface;
use Drupal\user\UserAuthInterface;
use Dr<PERSON>al\user\UserFloodControlInterface;
use Drupal\user\UserStorageInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Serializer\Serializer;

/**
 * Controller for handling MFA-protected login requests.
 */
class MfaLoginController extends UserAuthenticationController implements ContainerInjectionInterface {

  /**
   * The config factory.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * The authenticator service.
   *
   * @var \Drupal\alogin\AuthenticatorService
   */
  protected $authenticatorService;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Constructs a MfaLoginController object.
   *
   * @param \Drupal\user\UserFloodControlInterface $user_flood_control
   *   The user flood control service.
   * @param \Drupal\user\UserStorageInterface $user_storage
   *   The user storage.
   * @param \Drupal\Core\Access\CsrfTokenGenerator $csrf_token
   *   The CSRF token generator.
   * @param \Drupal\user\UserAuthenticationInterface|\Drupal\user\UserAuthInterface $user_auth
   *   The user authentication service.
   * @param \Drupal\Core\Routing\RouteProviderInterface $route_provider
   *   The route provider.
   * @param \Symfony\Component\Serializer\Serializer $serializer
   *   The serializer service.
   * @param array $serialization_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   The logger service.
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   The config factory.
   * @param \Drupal\alogin\AuthenticatorService $authenticator_service
   *   The authenticator service.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(UserFloodControlInterface $user_flood_control, UserStorageInterface $user_storage, CsrfTokenGenerator $csrf_token, UserAuthenticationInterface|UserAuthInterface $user_auth, RouteProviderInterface $route_provider, Serializer $serializer, array $serialization_formats, LoggerInterface $logger, ConfigFactoryInterface $config_factory, AuthenticatorService $authenticator_service, EntityTypeManagerInterface $entity_type_manager) {
    parent::__construct($user_flood_control, $user_storage, $csrf_token, $user_auth, $route_provider, $serializer, $serialization_formats, $logger);
    $this->configFactory = $config_factory;
    $this->authenticatorService = $authenticator_service;
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    if ($container->hasParameter('serializer.formats') && $container->has('serializer')) {
      $serializer = $container->get('serializer');
      $formats = $container->getParameter('serializer.formats');
    }
    else {
      $formats = ['json'];
      $encoders = [new \Symfony\Component\Serializer\Encoder\JsonEncoder()];
      $serializer = new \Symfony\Component\Serializer\Serializer([], $encoders);
    }

    return new static(
      $container->get('user.flood_control'),
      $container->get('entity_type.manager')->getStorage('user'),
      $container->get('csrf_token'),
      $container->get('user.auth'),
      $container->get('router.route_provider'),
      $serializer,
      $formats,
      $container->get('logger.factory')->get('user'),
      $container->get('config.factory'),
      $container->get('alogin.authenticator'),
      $container->get('entity_type.manager')
    );
  }

  /**
   * Custom login validation with MFA check.
   *
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The request object.
   *
   * @return \Symfony\Component\HttpFoundation\Response
   *   The response object.
   *
   * @throws \Symfony\Component\HttpKernel\Exception\BadRequestHttpException
   * @throws \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException
   */
  public function validateLoginRequest(Request $request) {
    $format = $this->getRequestFormat($request);
    $content = $request->getContent();

    if (empty($content)) {
      throw new BadRequestHttpException('Empty request body.');
    }

    $credentials = $this->serializer->decode($content, $format);

    if (empty($credentials['name'])) {
      throw new BadRequestHttpException('Missing credentials.name.');
    }

    if (empty($credentials['pass'])) {
      throw new BadRequestHttpException('Missing credentials.pass.');
    }

    // Load user by username.
    /** @var \Drupal\user\UserInterface[] $users */
    $users = $this->userStorage->loadByProperties(['name' => $credentials['name']]);

    if (count($users) !== 1) {
      throw new BadRequestHttpException('Sorry, unrecognized username or password.');
    }

    $user = reset($users);

    // Check if user is active.
    if (!$user->isActive()) {
      throw new AccessDeniedHttpException('The user has not been activated or is blocked.');
    }

    // Check if user has MFA configured.
    $user_has_mfa = $this->authenticatorService->exists($user->id());

    // If MFA is enabled but user doesn't have it configured, check if it's enforced.
    if (!$user_has_mfa) {
      $allow_enable_disable = $config->get('allow_enable_disable') ?? TRUE;
      $user_can_bypass = $user->hasPermission('alogin bypass enforced redirect');

      // If MFA is enforced and user can't bypass, deny access.
      if (!$allow_enable_disable && !$user_can_bypass) {
        throw new AccessDeniedHttpException('Two-factor authentication is required but not configured for this user.');
      }

      // If user can disable MFA or has bypass permission, allow normal login.
      return parent::login($request);
    }

    // If user has MFA configured, require MFA validation.
    // For API requests, we need to check if MFA token is provided.
    if (empty($credentials['mfa_token'])) {
      throw new AccessDeniedHttpException('Two-factor authentication token is required.');
    }

    // Validate the MFA token.
    $mfa_valid = $this->authenticatorService->verifyCode($credentials['mfa_token'], $user->id());

    if (!$mfa_valid) {
      throw new AccessDeniedHttpException('Invalid two-factor authentication token.');
    }

    // If MFA validation passes, proceed with normal login.
    return parent::login($request);
  }

  /**
   * Gets the format of the current request.
   *
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The current request.
   *
   * @return string
   *   The format of the request.
   */
  protected function getRequestFormat(Request $request) {
    $format = $request->getRequestFormat();
    if (!in_array($format, $this->serializerFormats)) {
      throw new BadRequestHttpException("Unrecognized format: $format.");
    }
    return $format;
  }

}
