{"name": "drupal/recommended-project", "description": "Project template for Drupal projects with a relocated document root", "type": "project", "license": "GPL-2.0-or-later", "homepage": "https://www.drupal.org/project/drupal", "support": {"docs": "https://www.drupal.org/docs/user_guide/en/index.html", "chat": "https://www.drupal.org/node/314178"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "require": {"bacon/bacon-qr-code": "^2.0", "composer/installers": "^2.0", "drupal/core-composer-scaffold": "^10.5", "drupal/core-project-message": "^10.5", "drupal/core-recommended": "^10.5", "drush/drush": "^12", "pragmarx/google2fa-qrcode": "^3.0"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "stable", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "phpstan/extension-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}, "sort-packages": true}, "extra": {"drupal-scaffold": {"locations": {"web-root": "web/"}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"]}, "drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                         </>", "<bg=blue;fg=white>  Congratulations, you’ve installed the Drupal codebase  </>", "<bg=blue;fg=white>  from the drupal/recommended-project template!          </>", "<bg=blue;fg=white>                                                         </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Install the site: https://www.drupal.org/docs/installing-drupal", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://www.drupal.org/support", "  * Get involved with the Drupal community:", "      https://www.drupal.org/getting-involved", "  * Remove the plugin that prints this message:", "      composer remove drupal/core-project-message"]}}}